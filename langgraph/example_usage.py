"""
使用示例：如何在其他文件中导入和使用需求文档分析功能

这个文件展示了如何使用 test_langgraph.py 中封装的分析函数
"""

import asyncio
import json
from datetime import datetime

# 导入封装的分析函数
from test_langgraph import analyze_requirement_document, analyze_requirement_sync


async def example_async_usage():
    """异步使用示例"""
    print("🚀 异步使用示例")
    print("=" * 50)
    
    # 示例需求文档
    requirement_doc = """
    我需要实现一个用户管理系统，包括以下功能：
    1. 用户注册和登录
    2. 用户信息管理
    3. 权限控制
    4. 密码重置功能
    
    技术要求：
    - 使用 Python Flask 框架
    - 数据库使用 SQLAlchemy ORM
    - 前端使用 Bootstrap
    - 需要支持 JWT 认证
    """
    
    try:
        # 调用异步分析函数
        result = await analyze_requirement_document(requirement_doc)
        
        # 输出分析结果
        print(f"📋 需求文档: {result['requirement_doc'][:100]}...")
        print(f"🔍 搜索次数: {result['search_count']}")
        print(f"⏰ 分析时间: {result['timestamp']}")
        print(f"🧵 线程ID: {result['thread_id']}")
        print(f"📊 消息数量: {result['messages_count']}")
        print(f"🤖 模型响应数量: {result['total_model_responses']}")
        print(f"✅ 分析完成: {result['is_complete']}")
        
        print("\n📝 最终分析结果:")
        print("=" * 50)
        print(result['final_understanding'])
        
        # 保存结果到文件（可选）
        output_file = f"example_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {output_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None


def example_sync_usage():
    """同步使用示例"""
    print("\n🚀 同步使用示例")
    print("=" * 50)
    
    # 示例需求文档
    requirement_doc = """
    开发一个简单的博客系统：
    1. 文章发布和编辑
    2. 评论功能
    3. 标签分类
    4. 搜索功能
    
    技术栈：
    - 后端：Django
    - 前端：Vue.js
    - 数据库：PostgreSQL
    """
    
    try:
        # 调用同步分析函数
        result = analyze_requirement_sync(requirement_doc)
        
        # 输出关键信息
        print(f"🔍 搜索次数: {result['search_count']}")
        print(f"✅ 分析完成: {result['is_complete']}")
        
        if result.get('error'):
            print(f"❌ 错误信息: {result['error']}")
        else:
            print("\n📝 最终分析结果:")
            print("=" * 50)
            print(result['final_understanding'])
        
        return result
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None


async def batch_analysis_example():
    """批量分析示例"""
    print("\n🚀 批量分析示例")
    print("=" * 50)
    
    requirements = [
        "实现一个在线购物车系统，支持商品添加、删除、结算功能",
        "开发一个任务管理工具，包括任务创建、分配、进度跟踪",
        "构建一个文件上传下载系统，支持多种文件格式和权限控制"
    ]
    
    results = []
    for i, req in enumerate(requirements, 1):
        print(f"\n📋 分析需求 {i}/{len(requirements)}")
        try:
            result = await analyze_requirement_document(req, thread_id=f"batch-{i}")
            results.append(result)
            print(f"✅ 需求 {i} 分析完成，搜索了 {result['search_count']} 次")
        except Exception as e:
            print(f"❌ 需求 {i} 分析失败: {e}")
            results.append(None)
    
    # 统计结果
    successful = len([r for r in results if r and r.get('is_complete')])
    print(f"\n📊 批量分析完成: {successful}/{len(requirements)} 成功")
    
    return results


async def main():
    """主函数：运行所有示例"""
    print("🎯 需求文档分析功能使用示例")
    print("=" * 60)
    
    # 1. 异步使用示例
    await example_async_usage()
    
    # 2. 同步使用示例
    example_sync_usage()
    
    # 3. 批量分析示例
    await batch_analysis_example()
    
    print("\n🎉 所有示例运行完成！")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
